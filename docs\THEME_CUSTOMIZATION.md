# Nuxt UI Theme Customization Guide

This guide explains how to use the custom color palette configured for this Nuxt 4 project with Nuxt UI.

## Color Palette Overview

Our custom color palette includes:

- **Primary (Blue)**: Professional blue for primary actions and branding
- **Secondary (Purple)**: Elegant purple for secondary elements
- **Accent (Emerald)**: Vibrant emerald for highlights and accents
- **Success (Green)**: Enhanced green for success states
- **Warning (Amber)**: Optimized amber for warnings
- **Error (Red)**: Enhanced red for error states
- **Neutral (Zinc)**: Sophisticated zinc for neutral elements

## Configuration Files

### 1. `nuxt.config.ts`
Defines which color aliases are available for components:

```typescript
export default defineNuxtConfig({
  ui: {
    theme: {
      colors: [
        'primary',
        'secondary', 
        'accent',
        'success',
        'warning',
        'error',
        'neutral'
      ]
    }
  }
})
```

### 2. `app.config.ts`
Maps color aliases to Tailwind color names:

```typescript
export default defineAppConfig({
  ui: {
    colors: {
      primary: 'blue',
      secondary: 'purple',
      accent: 'emerald',
      success: 'green',
      warning: 'amber',
      error: 'red',
      neutral: 'zinc'
    }
  }
})
```

### 3. `app/assets/css/main.css`
Defines custom color values and theme variables for light/dark modes.

## Using Custom Colors

### Button Components
```vue
<template>
  <UButton color="primary">Primary Action</UButton>
  <UButton color="secondary">Secondary Action</UButton>
  <UButton color="accent">Accent Action</UButton>
  <UButton color="success">Success Action</UButton>
  <UButton color="warning">Warning Action</UButton>
  <UButton color="error">Error Action</UButton>
  <UButton color="neutral">Neutral Action</UButton>
</template>
```

### Form Components
```vue
<template>
  <UInput color="primary" placeholder="Primary input" />
  <UTextarea color="secondary" placeholder="Secondary textarea" />
  <USelect color="accent" :options="options" />
  <UToggle color="success" v-model="toggle" />
</template>
```

### Alert Components
```vue
<template>
  <UAlert color="success" title="Success!" description="Operation completed successfully." />
  <UAlert color="warning" title="Warning!" description="Please review your input." />
  <UAlert color="error" title="Error!" description="Something went wrong." />
</template>
```

### Badge Components
```vue
<template>
  <UBadge color="primary">Primary</UBadge>
  <UBadge color="accent">Accent</UBadge>
  <UBadge color="success">Success</UBadge>
</template>
```

### Card Components
```vue
<template>
  <UCard>
    <template #header>
      <h3 class="text-primary">Primary Heading</h3>
    </template>
    <p class="text-muted">Card content with custom colors.</p>
  </UCard>
</template>
```

## CSS Classes

You can also use the colors directly as CSS classes:

### Text Colors
```vue
<template>
  <span class="text-primary">Primary text</span>
  <span class="text-secondary">Secondary text</span>
  <span class="text-accent">Accent text</span>
  <span class="text-success">Success text</span>
  <span class="text-warning">Warning text</span>
  <span class="text-error">Error text</span>
</template>
```

### Background Colors
```vue
<template>
  <div class="bg-primary text-white p-4">Primary background</div>
  <div class="bg-accent text-white p-4">Accent background</div>
  <div class="bg-muted p-4">Muted background</div>
</template>
```

### Border Colors
```vue
<template>
  <div class="border border-primary">Primary border</div>
  <div class="border border-accent">Accent border</div>
  <div class="border border-default">Default border</div>
</template>
```

## Dark Mode Support

The color system automatically adapts to dark mode:

- Light mode uses darker shades (600) for better contrast on light backgrounds
- Dark mode uses lighter shades (400) for better contrast on dark backgrounds
- Background and text colors automatically adjust based on the color mode

### Toggling Color Mode
```vue
<template>
  <UButton @click="toggleColorMode">
    {{ $colorMode.value === 'dark' ? 'Light Mode' : 'Dark Mode' }}
  </UButton>
</template>

<script setup>
const colorMode = useColorMode()

const toggleColorMode = () => {
  colorMode.preference = colorMode.value === 'dark' ? 'light' : 'dark'
}
</script>
```

## Accessibility Considerations

Our color palette follows WCAG accessibility guidelines:

- **Contrast Ratios**: All colors meet AA standards for contrast
- **Color Blindness**: Colors are distinguishable for common types of color blindness
- **Semantic Meaning**: Colors follow conventional meanings (green for success, red for error, etc.)

## Customizing Further

To add new colors or modify existing ones:

1. **Add to `nuxt.config.ts`**: Include the new color alias in the `colors` array
2. **Map in `app.config.ts`**: Map the alias to a Tailwind color name
3. **Define in CSS**: Add the color definitions in `main.css` if using custom values
4. **Update theme variables**: Add light/dark mode variables if needed

Example of adding a new "info" color:

```typescript
// nuxt.config.ts
colors: ['primary', 'secondary', 'accent', 'info', 'success', 'warning', 'error', 'neutral']

// app.config.ts
colors: {
  // ... existing colors
  info: 'cyan'
}
```

## Best Practices

1. **Consistency**: Use the same color for similar actions across your app
2. **Hierarchy**: Use primary for main actions, secondary for less important ones
3. **Feedback**: Use success/warning/error colors for user feedback
4. **Accessibility**: Always test color combinations for sufficient contrast
5. **Semantic Meaning**: Stick to conventional color meanings when possible

## Testing Your Theme

Use the `ThemeShowcase.vue` component to test your color palette:

```vue
<template>
  <ThemeShowcase />
</template>
```

This component displays all colors in various contexts and allows you to toggle between light and dark modes to ensure proper contrast and readability.
