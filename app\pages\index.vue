<template>
  <UContainer class="py-8">
    <!-- Header -->
    <div class="text-center mb-8">
      <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">
        Business Analyst Report - Teacher Grading Tool
      </h1>
      <p class="text-lg text-gray-600 dark:text-gray-300 mb-2">
        Performance Task #2: Exhibit B - The Business Analyst Report
      </p>
      <p class="text-sm text-gray-500 dark:text-gray-400">
        General Mathematics 11 - Innovatech Nexus-Pebble Analysis
      </p>
    </div>

    <!-- Navigation Tabs -->
    <div class="mb-8">
      <UTabs v-model="activeTab" :items="tabs" class="w-full" />
    </div>

    <!-- Tab Content -->
    <div class="min-h-[600px]">
      <!-- Grade Student Tab -->
      <div v-if="activeTab === 0">
        <GradingInterface @switch-to-overview="activeTab = 1" />
      </div>

      <!-- View All Grades Tab -->
      <div v-if="activeTab === 1">
        <GradeOverview @edit-grade="editGradeFromOverview" />
      </div>

      <!-- Theme Showcase Tab -->
      <div v-if="activeTab === 2">
        <ThemeShowcase />
      </div>
    </div>
  </UContainer>
</template>

<script setup lang="ts">
// Reactive state
const activeTab = ref(0)

// Tab configuration
const tabs = [
  {
    label: 'Grade Student',
    icon: 'i-heroicons-academic-cap',
    description: 'Grade individual student performance tasks'
  },
  {
    label: 'View All Grades',
    icon: 'i-heroicons-chart-bar',
    description: 'Manage and review all student grades'
  },
  {
    label: 'Theme Showcase',
    icon: 'i-heroicons-swatch',
    description: 'View custom color palette and components'
  }
]

// Handle edit grade from overview
const editGradeFromOverview = (_analystId: number) => {
  // Switch to grading tab and load the student
  activeTab.value = 0
  // The grading interface will handle loading the existing grade
}

// Set page meta
definePageMeta({
  title: 'Business Analyst Report - Teacher Grading Tool'
})

// SEO
useSeoMeta({
  title: 'Business Analyst Report - Teacher Grading Tool',
  description: 'Teacher grading tool for General Mathematics 11 performance task evaluation',
  ogTitle: 'Business Analyst Report - Teacher Grading Tool',
  ogDescription: 'Teacher grading tool for General Mathematics 11 performance task evaluation'
})
</script>